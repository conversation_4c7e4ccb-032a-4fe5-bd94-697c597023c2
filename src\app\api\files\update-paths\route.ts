"use server";
import { prisma } from "@/src/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    console.log("Iniciando atualização de caminhos de arquivos...");

    // Buscar todos os arquivos que começam com "photos/"
    const filesToUpdate = await prisma.file.findMany({
      where: {
        path: {
          startsWith: "photos/"
        }
      },
      select: {
        id: true,
        path: true,
        name: true
      }
    });

    console.log(`Encontrados ${filesToUpdate.length} arquivos para atualizar`);

    if (filesToUpdate.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Nenhum arquivo encontrado para atualizar",
        totalFiles: 0,
        updatedFiles: 0,
        logs: []
      });
    }

    const logs: Array<{
      fileId: string;
      fileName: string;
      oldPath: string;
      newPath: string;
      status: string;
    }> = [];

    let updatedCount = 0;

    // Atualizar cada arquivo
    for (const file of filesToUpdate) {
      try {
        const newPath = `matriz/${file.path}`;
        
        await prisma.file.update({
          where: { id: file.id },
          data: { path: newPath }
        });

        updatedCount++;
        logs.push({
          fileId: file.id,
          fileName: file.name,
          oldPath: file.path,
          newPath: newPath,
          status: "updated"
        });

        console.log(`Arquivo atualizado: ${file.name} - ${file.path} -> ${newPath}`);
      } catch (error) {
        console.error(`Erro ao atualizar arquivo ${file.id}:`, error);
        logs.push({
          fileId: file.id,
          fileName: file.name,
          oldPath: file.path,
          newPath: `matriz/${file.path}`,
          status: "error"
        });
      }
    }

    console.log(`Atualização concluída. ${updatedCount} arquivos atualizados de ${filesToUpdate.length} encontrados.`);

    return NextResponse.json({
      success: true,
      message: `Atualização concluída com sucesso`,
      totalFiles: filesToUpdate.length,
      updatedFiles: updatedCount,
      logs: logs
    });

  } catch (error) {
    console.error("Erro durante a atualização de caminhos:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : "Erro desconhecido"
      },
      { status: 500 }
    );
  }
}
