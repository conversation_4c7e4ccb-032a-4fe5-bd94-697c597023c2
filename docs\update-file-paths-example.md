# Atualização de Caminhos de Arquivos

Esta documentação explica como usar a API e action criadas para atualizar os caminhos dos arquivos na tabela File, adicionando o prefixo "matriz/" aos caminhos que começam com "photos/".

## API Endpoint

### POST `/api/files/update-paths`

Atualiza todos os arquivos na tabela File que possuem caminhos começando com "photos/", adicionando o prefixo "matriz/".

**Exemplo de uso:**

```javascript
// Chamada via fetch
const response = await fetch('/api/files/update-paths', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  }
});

const result = await response.json();
console.log(result);
```

**Resposta de sucesso:**
```json
{
  "success": true,
  "message": "Atualização concluída com sucesso",
  "totalFiles": 15,
  "updatedFiles": 15,
  "logs": [
    {
      "fileId": "clx123...",
      "fileName": "img-20250722-wa0300.jpg",
      "oldPath": "photos/c9cc410e-09f6-4b40-9627-fced9c9f6f9e/img-20250722-wa0300.jpg",
      "newPath": "matriz/photos/c9cc410e-09f6-4b40-9627-fced9c9f6f9e/img-20250722-wa0300.jpg",
      "status": "updated"
    }
  ]
}
```

## Server Action

### `updateFilePathsWithMatrizPrefix()`

Função server action que pode ser chamada diretamente em componentes React ou outras server actions.

**Exemplo de uso:**

```typescript
import { updateFilePathsWithMatrizPrefix } from '@/src/actions/files';

// Em um componente ou server action
const handleUpdatePaths = async () => {
  const result = await updateFilePathsWithMatrizPrefix();
  
  if (result.success) {
    console.log(`${result.updatedFiles} arquivos atualizados de ${result.totalFiles} encontrados`);
    console.log('Logs:', result.logs);
  } else {
    console.error('Erro:', result.message);
  }
};
```

## Exemplo de Transformação

**Antes:**
```
photos/c9cc410e-09f6-4b40-9627-fced9c9f6f9e/img-20250722-wa0300.jpg
```

**Depois:**
```
matriz/photos/c9cc410e-09f6-4b40-9627-fced9c9f6f9e/img-20250722-wa0300.jpg
```

## Características

- ✅ Atualiza apenas arquivos que começam com "photos/"
- ✅ Adiciona o prefixo "matriz/" mantendo o caminho original
- ✅ Fornece logs detalhados de cada operação
- ✅ Trata erros individualmente por arquivo
- ✅ Retorna estatísticas completas da operação
- ✅ Disponível tanto como API quanto como server action

## Segurança

- A operação é idempotente (pode ser executada múltiplas vezes sem problemas)
- Apenas arquivos com caminhos específicos são afetados
- Logs detalhados permitem auditoria das mudanças
- Tratamento de erros individual por arquivo
